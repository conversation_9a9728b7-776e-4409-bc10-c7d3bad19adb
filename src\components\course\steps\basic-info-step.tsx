'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { CalendarIcon, Upload, X, Shuffle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CourseData } from '../course-creation-wizard';
import { toast } from 'sonner';

interface BasicInfoStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function BasicInfoStep({ data, onUpdate }: BasicInfoStepProps) {
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateCourseCode = () => {
    setIsGeneratingCode(true);
    // Simulate API call
    setTimeout(() => {
      const code = Math.random().toString(36).substring(2, 8).toUpperCase();
      onUpdate({ courseCode: code });
      setIsGeneratingCode(false);
      toast.success('Kode course berhasil dibuat');
    }, 1000);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('File harus berupa gambar');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Ukuran file maksimal 5MB');
      return;
    }

    // Create preview URL
    const previewUrl = URL.createObjectURL(file);
    onUpdate({ 
      coverImage: file, 
      coverImagePreview: previewUrl 
    });
    toast.success('Gambar berhasil diunggah');
  };

  const removeCoverImage = () => {
    if (data.coverImagePreview) {
      URL.revokeObjectURL(data.coverImagePreview);
    }
    onUpdate({ 
      coverImage: undefined, 
      coverImagePreview: undefined 
    });
  };

  return (
    <div className="space-y-6">
      {/* Course Name */}
      <div className="space-y-2">
        <Label htmlFor="courseName">Nama Course *</Label>
        <Input
          id="courseName"
          placeholder="Masukkan nama course"
          value={data.name}
          onChange={(e) => onUpdate({ name: e.target.value })}
        />
      </div>

      {/* Instructor */}
      <div className="space-y-2">
        <Label htmlFor="instructor">Nama Instruktur *</Label>
        <Input
          id="instructor"
          placeholder="Masukkan nama instruktur"
          value={data.instructor}
          onChange={(e) => onUpdate({ instructor: e.target.value })}
        />
      </div>

      {/* Course Code */}
      <div className="space-y-2">
        <Label htmlFor="courseCode">Kode Course *</Label>
        <div className="flex space-x-2">
          <Input
            id="courseCode"
            placeholder="Kode unik untuk course"
            value={data.courseCode}
            onChange={(e) => onUpdate({ courseCode: e.target.value.toUpperCase() })}
            className="flex-1"
          />
          <Button 
            type="button" 
            variant="outline" 
            onClick={generateCourseCode}
            disabled={isGeneratingCode}
          >
            <Shuffle className="w-4 h-4 mr-2" />
            {isGeneratingCode ? 'Membuat...' : 'Generate'}
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          Kode ini akan digunakan siswa untuk mendaftar ke course
        </p>
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">Deskripsi Course *</Label>
        <Textarea
          id="description"
          placeholder="Jelaskan tentang course ini..."
          value={data.description}
          onChange={(e) => onUpdate({ description: e.target.value })}
          rows={4}
        />
      </div>

      {/* Cover Image */}
      <div className="space-y-2">
        <Label>Cover Image</Label>
        {data.coverImagePreview ? (
          <div className="relative">
            <img
              src={data.coverImagePreview}
              alt="Course cover"
              className="w-full h-48 object-cover rounded-lg border"
            />
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="absolute top-2 right-2"
              onClick={removeCoverImage}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        ) : (
          <div 
            className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              Klik untuk upload cover image
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              PNG, JPG hingga 5MB
            </p>
          </div>
        )}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
        />
      </div>

      {/* Course Type */}
      <div className="space-y-2">
        <Label>Tipe Course *</Label>
        <Select
          value={data.type}
          onValueChange={(value: 'self_paced' | 'verified') => onUpdate({ type: value })}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="self_paced">
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">Self-paced</Badge>
                <span>Siswa belajar dengan kecepatan sendiri</span>
              </div>
            </SelectItem>
            <SelectItem value="verified">
              <div className="flex items-center space-x-2">
                <Badge variant="default">Verified</Badge>
                <span>Course dengan jadwal dan deadline</span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Enrollment Type */}
      <div className="space-y-2">
        <Label>Tipe Pendaftaran *</Label>
        <Select
          value={data.enrollmentType}
          onValueChange={(value: 'code' | 'invitation' | 'both' | 'purchase') => onUpdate({ enrollmentType: value })}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="code">
              <div className="flex items-center space-x-2">
                <Badge variant="outline">Kode</Badge>
                <span>Siswa mendaftar dengan kode</span>
              </div>
            </SelectItem>
            <SelectItem value="invitation">
              <div className="flex items-center space-x-2">
                <Badge variant="outline">Undangan</Badge>
                <span>Hanya dengan undangan</span>
              </div>
            </SelectItem>
            <SelectItem value="both">
              <div className="flex items-center space-x-2">
                <Badge variant="outline">Keduanya</Badge>
                <span>Kode atau undangan</span>
              </div>
            </SelectItem>
            <SelectItem value="purchase">
              <div className="flex items-center space-x-2">
                <Badge variant="default">Berbayar</Badge>
                <span>Siswa harus membeli</span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Price and Currency (only for purchase/both enrollment type) */}
      {data.enrollmentType === 'purchase' || data.enrollmentType === 'both' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="price">Harga *</Label>
            <Input
              id="price"
              type="number"
              placeholder="0"
              value={data.price || ''}
              onChange={(e) => onUpdate({ price: parseFloat(e.target.value) || 0 })}
              min="0"
              step="1000"
            />
          </div>
          <div className="space-y-2">
            <Label>Mata Uang *</Label>
            <Select
              value={data.currency || 'IDR'}
              onValueChange={(value) => onUpdate({ currency: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="IDR">IDR (Rupiah)</SelectItem>
                <SelectItem value="USD">USD (Dollar)</SelectItem>
                <SelectItem value="EUR">EUR (Euro)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Date Range (in the future, there should be a checkbox to use date range or not) */}
      {data.type === 'verified' || data.type === 'self_paced' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Tanggal Mulai</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !data.startDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {data.startDate ? (
                    format(data.startDate, "PPP", { locale: id })
                  ) : (
                    "Pilih tanggal mulai"
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={data.startDate}
                  onSelect={(date) => onUpdate({ startDate: date })}
                  disabled={(date) => date < new Date()}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label>Tanggal Selesai</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !data.endDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {data.endDate ? (
                    format(data.endDate, "PPP", { locale: id })
                  ) : (
                    "Pilih tanggal selesai"
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={data.endDate}
                  onSelect={(date) => onUpdate({ endDate: date })}
                  disabled={(date) => Boolean(date < new Date() || (data.startDate && date <= data.startDate))}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      )}

      {/* Course Type Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Informasi Tipe Course</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">Self-paced</Badge>
              </div>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Siswa belajar dengan kecepatan sendiri</li>
                <li>• Tidak ada deadline ketat</li>
                <li>• Akses selamanya setelah enrollment</li>
                <li>• Cocok untuk pembelajaran mandiri</li>
              </ul>
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Badge variant="default">Verified</Badge>
              </div>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Course dengan jadwal dan deadline</li>
                <li>• Sertifikat resmi setelah selesai</li>
                <li>• Monitoring progress lebih ketat</li>
                <li>• Cocok untuk pembelajaran formal</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}